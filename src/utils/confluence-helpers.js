/**
 * Confluence Helper Functions for Forge App
 *
 * This module contains helper functions for interacting with Confluence using the Forge API.
 * These functions are designed to work within the Forge runtime environment.
 */

import api, { route } from '@forge/api';

/**
 * Get all available Confluence spaces
 * @returns {Promise<Array>} Array of space objects
 */
export async function getSpaces() {
  try {
    const response = await api.asApp().requestConfluence(route`/wiki/api/v2/spaces`);
    
    if (!response.ok) {
      console.error(`Failed to fetch Confluence spaces: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    return data.results || [];
  } catch (error) {
    console.error('Error fetching Confluence spaces:', error);
    return [];
  }
}


/**
 * Get ALL pages from a space using space key with full pagination (titles only, no content)
 * @param {string} spaceKey - Space key to search in
 * @returns {Promise<Array>} Array of all page objects (titles and metadata only)
 */
export async function getAllPagesFromSpace(spaceKey) {
  try {
    // First, get the space to find its ID (API v2 requires space ID, not key)
    const space = await getSpaceByKey(spaceKey);
    if (!space) {
      console.error(`Space with key ${spaceKey} not found`);
      return [];
    }

    // Use the paginated method to get ALL pages
    return await getAllPagesFromSpaceById(space.id);
  } catch (error) {
    console.error(`Error fetching all pages from space ${spaceKey}:`, error);
    return [];
  }
}


/**
 * Get ALL pages from a space using cursor-based pagination (titles only, no content)
 * @param {string|number} spaceId - Space ID to search in
 * @returns {Promise<Array>} Array of all page objects (titles and metadata only)
 */
export async function getAllPagesFromSpaceById(spaceId) {
  const allPages = [];
  let cursor = null;
  const batchSize = 250; // Use maximum allowed limit for efficiency

  try {
    console.log(`Fetching ALL pages from space ID ${spaceId} using pagination...`);
    
    do {
      // Build request with cursor for pagination
      const response = cursor
        ? await api.asApp().requestConfluence(route`/wiki/api/v2/spaces/${spaceId}/pages?limit=${batchSize}&cursor=${encodeURIComponent(cursor)}`)
        : await api.asApp().requestConfluence(route`/wiki/api/v2/spaces/${spaceId}/pages?limit=${batchSize}`);

      if (!response.ok) {
        console.error(`Failed to fetch pages batch from space ID ${spaceId}: ${response.status} ${response.statusText}`);
        break;
      }

      const data = await response.json();
      const pages = data.results || [];
      allPages.push(...pages);

      // Check for next cursor in the response
      cursor = data._links?.next ? extractCursorFromUrl(data._links.next) : null;
      
      console.log(`Fetched ${pages.length} pages (total so far: ${allPages.length}) from space ID ${spaceId}`);
      
    } while (cursor);

    console.log(`Completed pagination: Retrieved ${allPages.length} total pages from space ID ${spaceId}`);
    return allPages;
  } catch (error) {
    console.error(`Error fetching all pages from space ID ${spaceId}:`, error);
    return allPages; // Return what we got so far
  }
}

/**
 * Helper function to extract cursor from a URL
 * @param {string} url - URL containing cursor parameter
 * @returns {string|null} Extracted cursor or null
 */
function extractCursorFromUrl(url) {
  try {
    const urlObj = new URL(url, 'https://example.com'); // Base URL needed for relative URLs
    return urlObj.searchParams.get('cursor');
  } catch (error) {
    console.error('Error extracting cursor from URL:', error);
    return null;
  }
}

/**
 * Get space information by key (helper function)
 * @param {string} spaceKey - Space key to fetch
 * @returns {Promise<Object|null>} Space object or null if not found
 */
export async function getSpaceByKey(spaceKey) {
  try {
    // Get all spaces and find the one with matching key
    const response = await api.asApp().requestConfluence(
      route`/wiki/api/v2/spaces?keys=${encodeURIComponent(spaceKey)}`
    );

    if (!response.ok) {
      console.error(`Failed to fetch space with key ${spaceKey}: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    const spaces = data.results || [];

    // Find the space with the exact key match
    const space = spaces.find(s => s.key === spaceKey);
    return space || null;
  } catch (error) {
    console.error(`Error fetching space with key ${spaceKey}:`, error);
    return null;
  }
}

/**
 * Get a specific page by ID with content
 * @param {string} pageId - Page ID to fetch
 * @returns {Promise<Object|null>} Page object with content or null if not found
 */
export async function getPageContent(pageId) {
  try {
    const response = await api.asApp().requestConfluence(
      route`/wiki/api/v2/pages/${pageId}?body-format=atlas_doc_format&expand=version,body`
    );
    
    if (!response.ok) {
      console.error(`Failed to fetch page ${pageId}: ${response.status} ${response.statusText}`);
      return null;
    }

    const pageData = await response.json();
    return pageData;
  } catch (error) {
    console.error(`Error fetching page content for ${pageId}:`, error);
    return null;
  }
}

/**
 * Get multiple pages by their IDs using CQL search (single API request)
 * @param {string|string[]} pageIds - Page ID or array of page IDs to fetch
 * @param {boolean} includeContent - Whether to include page content
 * @returns {Promise<Array>} Array of page objects
 */
export async function getPagesByIds(pageIds, includeContent = false) {
  try {
    const ids = Array.isArray(pageIds) ? pageIds : [pageIds];
    
    if (ids.length === 0) {
      return [];
    }

    // Use CQL search to get multiple pages in a single API request
    // For Confluence CQL, use 'id in' syntax without quotes for numeric IDs
    const cqlQuery = `id in (${ids.join(',')})`;
    const expandParams = includeContent ? 'body.storage,version,space' : 'version,space';

    console.log(`Executing CQL query: ${cqlQuery}`);

    const response = await api.asApp().requestConfluence(
      route`/wiki/rest/api/content/search?cql=${encodeURIComponent(cqlQuery)}&expand=${expandParams}&limit=${ids.length}`
    );
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unable to read error response');
      console.error(`Failed to fetch pages by CQL search: ${response.status} ${response.statusText}`);
      console.error(`CQL Query: ${cqlQuery}`);
      console.error(`Error response: ${errorText}`);
      return [];
    }

    const data = await response.json();
    const pages = data.results || [];
    
    console.log(`Retrieved ${pages.length} pages out of ${ids.length} requested using CQL batch search`);
    return pages;
  } catch (error) {
    console.error('Error fetching pages by IDs with CQL search:', error);
    
    // Fallback to individual requests if CQL search fails
    console.log('Falling back to individual page requests...');
    return await getPagesByIdsIndividual(pageIds, includeContent);
  }
}

/**
 * Get multiple pages by their IDs using individual requests (fallback method)
 * @param {string|string[]} pageIds - Page ID or array of page IDs to fetch
 * @param {boolean} includeContent - Whether to include page content
 * @returns {Promise<Array>} Array of page objects
 */
async function getPagesByIdsIndividual(pageIds, includeContent = false) {
  try {
    const ids = Array.isArray(pageIds) ? pageIds : [pageIds];
    const pages = [];

    console.log(`Falling back to individual requests for ${ids.length} pages`);
    
    // Fetch each page by ID individually
    for (const pageId of ids) {
      const expandParams = includeContent
        ? '?body-format=atlas_doc_format&expand=version,body,space'
        : '?expand=space,version';
      const response = await api.asApp().requestConfluence(
        route`/wiki/api/v2/pages/${pageId}${expandParams}`
      );
      
      if (response.ok) {
        const pageData = await response.json();
        pages.push(pageData);
      } else {
        console.error(`Failed to fetch page ${pageId}: ${response.status} ${response.statusText}`);
      }
    }

    console.log(`Individual requests completed: retrieved ${pages.length} pages out of ${ids.length} requested`);
    return pages;
  } catch (error) {
    console.error('Error fetching pages by IDs individually:', error);
    return [];
  }
}

/**
 * Search for pages by title across all spaces
 * @param {string} title - Page title to search for
 * @param {number} limit - Maximum results to return
 * @returns {Promise<Array>} Array of matching pages
 */
export async function searchPagesByTitle(title, limit = 10) {
  try {
    const response = await api.asApp().requestConfluence(
      route`/wiki/api/v2/pages?title=${encodeURIComponent(title)}&limit=${limit}&expand=space,version`
    );
    
    if (!response.ok) {
      console.error(`Failed to search pages by title: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    return data.results || [];
  } catch (error) {
    console.error('Error searching pages by title:', error);
    return [];
  }
}



/**
 * Get ALL pages from multiple spaces with full pagination (titles only, no content)
 * @param {Array} spaceKeys - Array of space keys to search in
 * @returns {Promise<Array>} Array of all page objects with metadata (titles only)
 */
export async function getAllPagesFromSpaces(spaceKeys) {
  try {
    if (!spaceKeys || spaceKeys.length === 0) {
      return [];
    }

    console.log(`Fetching ALL pages from ${spaceKeys.length} spaces with full pagination...`);

    // Fetch ALL pages from all spaces in parallel using pagination
    const spacePromises = spaceKeys.map(async (spaceKey) => {
      try {
        const pages = await getAllPagesFromSpace(spaceKey);
        // Add space key to each page for context
        return pages.map(page => ({
          ...page,
          spaceKey: spaceKey,
          pageId: page.id,
          title: page.title
        }));
      } catch (error) {
        console.error(`Error fetching all pages from space ${spaceKey}:`, error);
        return [];
      }
    });

    const spacePageResults = await Promise.all(spacePromises);
    
    // Flatten results and deduplicate by page ID
    const pageMap = new Map();
    for (const spacePages of spacePageResults) {
      for (const page of spacePages) {
        if (!pageMap.has(page.pageId)) {
          pageMap.set(page.pageId, page);
        }
      }
    }

    const uniquePages = Array.from(pageMap.values());
    console.log(`Retrieved ${uniquePages.length} unique pages from ${spaceKeys.length} spaces (with full pagination)`);
    
    return uniquePages;
  } catch (error) {
    console.error('Error fetching all pages from multiple spaces:', error);
    return [];
  }
}


/**
 * Get space information by key
 * @param {string} spaceKey - Space key to fetch
 * @returns {Promise<Object|null>} Space object or null if not found
 */
export async function getSpace(spaceKey) {
  try {
    const response = await api.asApp().requestConfluence(route`/wiki/api/v2/spaces/${spaceKey}`);
    
    if (!response.ok) {
      console.error(`Failed to fetch space ${spaceKey}: ${response.status} ${response.statusText}`);
      return null;
    }

    const spaceData = await response.json();
    return spaceData;
  } catch (error) {
    console.error(`Error fetching space ${spaceKey}:`, error);
    return null;
  }
}
